package com.cirpoint.service.archive;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.cirpoint.service.OnlineConfirmService;
import com.cirpoint.service.QualityReportService;
import com.cirpoint.util.ArchiveLogger;
import com.cirpoint.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;

import static com.cirpoint.util.CommonUtil.removeDecimalZero;

/**
 * 确认表档案服务类
 */
@Slf4j
@Service
public class ArchiveQRBService extends ArchiveBaseService {

	@Autowired
	private ArchivePackageService archivePackageService;

	@Autowired
	private QualityReportService qualityReportService;

	@Autowired
	private OnlineConfirmService onlineConfirmService;

	// 确认表案卷序号计数器
	private final AtomicInteger rollQRBCounter = new AtomicInteger(1);

	/**
	 * 收集确认表的数据包（包含AIT质量确认表和发射场确认表）
	 */
	public void collectQRBPackage(String phaseTreeId, String pushUsername, String pushUserFullname, JSONObject phaseModelObj, ArchiveLogger logger, GlobalRollSortManager globalSortManager) {
		try {
			logger.info("开始收集确认表数据包");
			logger.info(String.format("阶段树ID: %s, 推送用户: %s", phaseTreeId, pushUserFullname));
			String packageType = "QRB";
			String pkgPath = generateUniquePath(1, packageType);
			String logId = insertArchivePushLog(phaseTreeId, phaseModelObj, packageType,
					phaseModelObj.getStr("NODENAME") + "_确认表", pkgPath);

			if (logId == null) {
				logger.info("确认表数据包已存在且成功推送，跳过处理");
				return;
			}

			logger.info("创建确认表数据包目录: " + pkgPath + ", 数据包名称: " + phaseModelObj.getStr("NODENAME") + "_确认表");
			FileUtil.mkdir(pkgPath);
			createQRBPackage(phaseTreeId, pushUsername, pushUserFullname, pkgPath, phaseModelObj, logger, globalSortManager);

			if (!checkDirectories(pkgPath)) {
				logger.info("数据包目录检查未通过，清理目录并删除日志记录");
				FileUtil.del(pkgPath);
				//删除日志记录
				String deleteSql = "DELETE FROM ARCHIVE_PUSH_LOG WHERE ID = '" + logId + "'";

				logger.info("=== 执行SQL语句 [collectQRBPackage - 删除日志记录] ===");
				logger.info("SQL语句: " + deleteSql);
				logger.info("参数: logId = " + logId);

				Util.postCommandSql(deleteSql);
				return;
			}

			logger.info("数据包目录检查通过，开始更新收集状态");
			updateCollectStatus(logId, "SUCCESS", null,
					countPdfFiles(pkgPath), 1, countArchiveFiles(pkgPath));

			logger.info("开始推送确认表数据包");
			archivePackageService.zipAndPushPackage(pkgPath, logId);
			logger.info("确认表数据包推送完成");

		} catch (Exception e) {
			logger.error("收集确认表数据包过程中发生错误", e);
			throw e;
		}
	}

	private boolean checkDirectories(String pkgPath) {
		return FileUtil.exist(pkgPath + "AIT_QRB") ||
				FileUtil.exist(pkgPath + "FCS_QRB");
	}

	/**
	 * 创建确认表的数据包xml
	 */
	private void createQRBPackage(String phaseTreeId, String pushUsername, String pushUserFullname, String pkgPath, JSONObject phaseModelObj, ArchiveLogger logger, GlobalRollSortManager globalSortManager) {
		createPackageXml(pkgPath, (roll, archive) ->
				createQRBColl(pkgPath, phaseTreeId, pushUsername, pushUserFullname, roll, archive, phaseModelObj, logger, globalSortManager)
		);
	}

	private void createQRBColl(String pkgPath, String phaseTreeId, String pushUsername, String pushUserFullname, Element rollEl, Element archiveEl, JSONObject phaseModelObj, ArchiveLogger logger, GlobalRollSortManager globalSortManager) {
		logger.info("开始创建确认表案卷");
		String phaseNodeName = phaseModelObj.getStr("NODENAME");
		String innerId = phaseTreeId + "_QRB_roll";
		String rollName = "确认表案卷";
		String rollId = String.format("0802(%s)QRB-%03d", phaseNodeName, rollQRBCounter.getAndIncrement());
		String rollXH = Convert.toStr(rollQRBCounter.get());
		String rollDocName = phaseNodeName + "_QRB_roll";
		int rollSort = globalSortManager.getNextQRBSort();

		logger.info(String.format("创建确认表案卷XML，案卷号: %s, 案卷名称: %s, 排序号: %d", rollId, rollName, rollSort));
		createRollXml(innerId, rollName, rollId, pushUserFullname, DateUtil.today(), rollXH,
				pkgPath, rollDocName, phaseNodeName, rollEl, rollSort);
		logger.info("确认表案卷XML创建完成");

		// 创建两个异步任务
		logger.info("开始创建AIT质量确认表和发射场确认表异步任务");
		CompletableFuture<Void> aitFuture = CompletableFuture.runAsync(() -> {
			try {
				logger.info("开始收集AIT质量确认表");
				collectAIT_QRB(pkgPath, phaseTreeId, pushUsername, archiveEl, phaseModelObj, logger, rollId, globalSortManager);
				logger.info("AIT质量确认表收集完成");
			} catch (Exception e) {
				logger.error("收集AIT质量确认表失败", e);
				throw e;
			}
		});

		CompletableFuture<Void> fcsFuture = CompletableFuture.runAsync(() -> {
			try {
				logger.info("开始收集发射场确认表");
				collectFCS_QRB(pkgPath, archiveEl, phaseModelObj, logger, rollId, globalSortManager);
				logger.info("发射场确认表收集完成");
			} catch (Exception e) {
				logger.error("收集发射场确认表失败", e);
				throw e;
			}
		});

		// 等待所有任务完成
		try {
			logger.info("等待AIT质量确认表和发射场确认表收集任务完成");
			CompletableFuture.allOf(aitFuture, fcsFuture).join();
			logger.info("所有确认表收集任务已完成");
		} catch (Exception e) {
			logger.error("收集确认表过程中发生错误", e);
			throw new RuntimeException("收集确认表失败", e);
		}
	}

	/**
	 * 收集AIT质量确认表
	 */
	private void collectAIT_QRB(String pkgPath, String phaseTreeId, String pushUsername, Element archiveEl, JSONObject phaseModelObj, ArchiveLogger logger, String rollId, GlobalRollSortManager globalSortManager) {
		logger.info("开始查询过程节点信息");
		JSONArray processNodes = queryProcessNodeId(phaseTreeId, logger);
		logger.info(String.format("找到 %d 个过程节点", processNodes.size()));

		// 先检查是否有任何节点存在确认表
		boolean hasQualityReports = false;
		logger.info("开始检查节点是否存在质量确认表");
		for (int i = 0; i < processNodes.size(); i++) {
			JSONObject processNode = processNodes.getJSONObject(i);
			String processNodeId = processNode.getStr("TREEID");
			String sql = "select * from QUALITY_REPORT where TREE_ID = " + processNodeId;

			logger.info("=== 执行SQL查询 [collectAIT_QRB - 检查质量确认表] ===");
			logger.info("SQL语句: " + sql);
			logger.info("参数: processNodeId = " + processNodeId);
			logger.info("参数: 节点名称 = " + processNode.getStr("NODENAME", ""));
			JSONArray qualityReports = Util.postQuerySql(sql);
			if (!qualityReports.isEmpty()) {
				hasQualityReports = true;
				logger.info(String.format("节点[%s]存在质量确认表", processNode.getStr("NODENAME")));
				break;
			}
		}

		// 如果没有任何确认表,直接返回
		if (!hasQualityReports) {
			logger.info(String.format("型号[%s]没有找到任何AIT质量确认表,跳过处理", phaseModelObj.getStr("MODEL_NAME")));
			return;
		}

		// 创建归档目录
		String archiveFolderPath = pkgPath + "AIT_QRB\\";
		logger.info("创建AIT质量确认表归档目录: " + archiveFolderPath);
		FileUtil.mkdir(archiveFolderPath);

		// 创建所有导出PDF任务的Future列表
		List<CompletableFuture<Void>> pdfFutures = new ArrayList<>();
		logger.info("开始创建PDF导出任务");

		// 先收集所有有确认表的节点并按节点名称排序
		List<JSONObject> validNodes = new ArrayList<>();
		for (int i = 0; i < processNodes.size(); i++) {
			JSONObject processNode = processNodes.getJSONObject(i);
			String processNodeId = processNode.getStr("TREEID");
			String treeNodeName = processNode.getStr("NODENAME");

			//查询节点下是否存在确认表QUALITY_REPORT
			String sql = "select * from QUALITY_REPORT where TREE_ID = " + processNodeId;
			JSONArray qualityReports = Util.postQuerySql(sql);
			if (!qualityReports.isEmpty()) {
				validNodes.add(processNode);
			}
		}

		// 按节点名称排序
		validNodes.sort((a, b) -> a.getStr("NODENAME", "").compareTo(b.getStr("NODENAME", "")));
		logger.info(String.format("按节点名称排序完成，共有 %d 个节点存在质量确认表", validNodes.size()));

		for (int i = 0; i < validNodes.size(); i++) {
			JSONObject processNode = validNodes.get(i);
			String processNodeId = processNode.getStr("TREEID");
			String processNodePId = processNode.getStr("PARENTID");
			String treeNodeName = processNode.getStr("NODENAME");
			final int archXH = i + 1; // 档案序号从1开始

			logger.info(String.format("节点[%s]存在质量确认表，创建PDF导出任务，序号: %d", treeNodeName, archXH));
			// 为每个PDF导出创建一个异步任务
			CompletableFuture<Void> pdfFuture = CompletableFuture.runAsync(() -> {
				try {
					// 为每个任务生成唯一的临时目录
					String uniqueTempPath = tempPath + UUID.randomUUID() + "\\";
					logger.info(String.format("创建临时目录用于导出PDF: %s", uniqueTempPath));
					FileUtil.mkdir(uniqueTempPath);

					//存在确认表，则生成pdf文件档案
					logger.info(String.format("开始导出节点[%s]的PDF文件", treeNodeName));
					JSONObject exportMorePdfRes = qualityReportService.exportMorePdf(
							"T_" + processNodeId,
							"T_" + processNodePId,
							uniqueTempPath,
							pushUsername,
							false);
					if (exportMorePdfRes.getBool("success")) {
						String srcFilePath = exportMorePdfRes.getStr("data");
						String fileName = treeNodeName + "_AIT确认表.pdf";
						String innerId = removeDecimalZero(processNodeId) + "_AIT_QRB";
						logger.info(String.format("创建PDF文件档案XML，文件名: %s, 序号: %d", fileName, archXH));
						createConfirmArch(srcFilePath, archiveFolderPath, fileName, innerId, removeDecimalZero(String.valueOf(archXH)), phaseModelObj, archiveEl, pkgPath, rollId, archXH);
						// 清理临时目录
						logger.info("清理临时目录: " + uniqueTempPath);
						FileUtil.del(uniqueTempPath);
						logger.info(String.format("节点[%s]的PDF文件处理完成", treeNodeName));
					} else {
						logger.info(String.format("导出节点[%s]的PDF文件失败: %s", treeNodeName, exportMorePdfRes.getStr("msg")));
					}
				} catch (Exception e) {
					logger.error(String.format("处理节点[%s]的PDF文件失败", treeNodeName), e);
					throw e;
				}
			});
			pdfFutures.add(pdfFuture);
		}

		// 等待所有PDF导出任务完成
		try {
			logger.info(String.format("等待 %d 个PDF导出任务完成", pdfFutures.size()));
			CompletableFuture.allOf(pdfFutures.toArray(new CompletableFuture[0])).join();
			logger.info("所有PDF导出任务已完成");
		} catch (Exception e) {
			logger.error("等待AIT质量确认表PDF导出任务完成时发生错误", e);
			throw new RuntimeException("AIT质量确认表PDF导出失败", e);
		}

	}

	/**
	 * 收集发射场确认表
	 */
	private void collectFCS_QRB(String pkgPath, Element archiveEl, JSONObject phaseModelObj, ArchiveLogger logger, String rollId, GlobalRollSortManager globalSortManager) {
		String phaseName = phaseModelObj.getStr("PHASE_NAME");
		String modelName = phaseModelObj.getStr("MODEL_NAME");

		if (!StrUtil.equals(phaseName, "正样")) {
			logger.info(String.format("阶段[%s]不需要收集发射场确认表，跳过处理", phaseName));
			return;
		}

		logger.info(String.format("开始查询型号[%s]的发射场确认表", modelName));
		//查询发射场确认表
		String sql = "select * from LAUNCH_CONFIRM where TYPE = 'model' and NAME='" + modelName + "'";
		logger.info("执行SQL: " + sql);
		JSONArray modelNodes = Util.postQuerySql(sql);
		if (modelNodes.isEmpty()) {
			logger.info(String.format("型号[%s]没有发射场确认表数据，跳过处理", modelName));
			return;
		}

		String id = modelNodes.getJSONObject(0).getStr("ID");
		String pid = modelNodes.getJSONObject(0).getStr("PID");
		log.info("发射场id:" + id);
		logger.info("开始导出发射场确认表PDF文件");
		//存在型号确认表，则生成pdf文件档案
		JSONObject exportMorePdfRes = onlineConfirmService.exportMorePdf(id, pid, "Thing.Fn.LaunchOnlineConfirm", tempPath, pdfFontPath, false);
		if (!exportMorePdfRes.getBool("success")) {
			logger.error(String.format("导出发射场确认表PDF失败: modelName=%s, 失败原因: %s", modelName, exportMorePdfRes.getStr("msg")));
			return;
		}

		String archiveFolderPath = pkgPath + "FCS_QRB\\";
		logger.info("创建发射场确认表归档目录: " + archiveFolderPath);
		FileUtil.mkdir(archiveFolderPath);

		String srcFilePath = exportMorePdfRes.getStr("data");
		String fileName = "发射场确认表.pdf";
		String innerId = removeDecimalZero(id) + "_FSC_QRB";

		// 发射场确认表排在AIT确认表之后，需要计算正确的档案排序号
		// 先查询AIT确认表的数量来确定发射场确认表的起始序号
		JSONArray processNodes = queryProcessNodeId(phaseModelObj.getStr("TREEID"), logger);
		int aitCount = 0;
		for (int i = 0; i < processNodes.size(); i++) {
			JSONObject processNode = processNodes.getJSONObject(i);
			String processNodeId = processNode.getStr("TREEID");
			String sql = "select * from QUALITY_REPORT where TREE_ID = " + processNodeId;
			JSONArray qualityReports = Util.postQuerySql(sql);
			if (!qualityReports.isEmpty()) {
				aitCount++;
			}
		}
		int fcsArchSort = aitCount + 1; // 发射场确认表排序号 = AIT确认表数量 + 1

		logger.info(String.format("创建PDF文件档案XML，文件名: %s, 序号: 1, 排序号: %d", fileName, fcsArchSort));
		createConfirmArch(srcFilePath, archiveFolderPath, fileName, innerId, "1", phaseModelObj, archiveEl, pkgPath, rollId, fcsArchSort);
		logger.info("发射场确认表PDF文件处理完成");
	}

	/**
	 * 查询所有的过程节点
	 */
	public JSONArray queryProcessNodeId(String phaseTreeId, ArchiveLogger logger) {
		//所有可以存放确认表的过程节点集合
		JSONArray processNodes = new JSONArray();
		//1、获取所有专业节点
		String sql = "select TREEID, PARENTID, NODENAME from DATAPACKAGETREE where NODETYPE='dir' start with TREEID = " + phaseTreeId + " connect by prior TREEID = PARENTID ORDER BY NODESORT";
		logger.info("执行SQL: " + sql);
		JSONArray dirNodes = Util.postQuerySql(sql);
		for (int i = 0; i < dirNodes.size(); i++) {
			JSONObject dirNode = dirNodes.getJSONObject(i);
			String dirNodeId = dirNode.getStr("TREEID");
			//获取dirNodeId下的子节点
			String sql2 = "select TREEID, PARENTID, NODENAME from DATAPACKAGETREE where PARENTID = " + dirNodeId + " ORDER BY NODESORT";
			logger.info("执行SQL: " + sql2);
			JSONArray childNodes = Util.postQuerySql(sql2);
			if (!childNodes.isEmpty()) {
				for (int j = 0; j < childNodes.size(); j++) {
					JSONObject childNode = childNodes.getJSONObject(j);
					processNodes.add(childNode);
				}
			} else {
				processNodes.add(dirNode);
			}
		}
		return processNodes;
	}

	/**
	 * 创建确认表档案XML和PDF文件
	 */
	private void createConfirmArch(String srcFilePath, String archiveFolderPath, String fileName,
								   String innerId, String archXH, JSONObject phaseModelObj, Element archiveEl, String pkgPath, String rollId, int archSort) {
		createArchXml(srcFilePath, archiveFolderPath, fileName, innerId, archXH, fileName,
				getSecretId(""), DateUtil.today(), rollId, phaseModelObj, archiveEl, pkgPath, archSort);
	}
}
