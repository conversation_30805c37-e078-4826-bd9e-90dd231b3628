package com.cirpoint.service.archive;

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.cirpoint.util.Util;
import com.cirpoint.util.ws.client.UploadFileAxisClientEx;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.function.BiConsumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 档案数据包服务类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ArchivePackageService extends ArchiveBaseService {

	/**
	 * 推送数据包文件到档案系统
	 */
	public void pushFile(File zipFile) throws Exception {
		JSONObject j = getAxisJson();
		UploadFileAxisClientEx client = new UploadFileAxisClientEx();
		client.setFileSize(j.getInt("fileSize"));
		client.setWsUrl(j.getStr("wsUrl"));
		client.setMethod(j.getStr("method"));
		client.setAuth(j.getStr("auth"));
		client.setDataPackage(j.getStr("dataPackage"));
		client.setNamespaceURL(j.getStr("namespaceURL"));
		client.upload(zipFile);
	}

	/**
	 * 检查是否有正在进行的收集任务
	 */
	public boolean hasRunningTask(String phaseTreeId) {
		String sql = "SELECT COUNT(1) as CNT FROM ARCHIVE_PUSH_LOG " +
				"WHERE PHASE_TREE_ID = '" + phaseTreeId + "' " +
				"AND (COLLECT_STATUS = 'COLLECTING' OR PUSH_STATUS = 'PUSHING')";

		JSONArray result = Util.postQuerySql(sql);
		if (!result.isEmpty()) {
			return result.getJSONObject(0).getInt("CNT") > 0;
		}
		return false;
	}

	/**
	 * 压缩并推送数据包
	 */
	public void zipAndPushPackage(String pkgPath, String logId) {
		String zipPath;
		try {
			zipPath = pkgPath.substring(0, pkgPath.length() - 1) + ".zip";
			// 压缩数据包文件夹
			cn.hutool.core.util.ZipUtil.zip(pkgPath, zipPath);
			// 更新推送中状态
			updatePushStatus(logId, "PUSHING", null, zipPath);
			// 推送到档案系统
			File zipFile = new File(zipPath);
            pushFile(zipFile);
			// 更新推送成功状态
			updatePushStatus(logId, "SUCCESS", null, zipPath);
			log.info("数据包[{}]推送成功", pkgPath);
		} catch (Exception e) {
			String errorMsg = e.getMessage();
			// 处理连接超时错误
			if (errorMsg != null && errorMsg.contains("Connection timed out")) {
				errorMsg = "连接档案系统超时，请检查网络连接后重试";
			}
			// 更新推送失败状态
			updatePushStatus(logId, "FAILED", errorMsg, null);
			log.error("数据包[{}]推送失败", pkgPath, e);
			throw new RuntimeException("数据包推送失败: " + errorMsg, e);
		} finally {
			try {
				// 删除数据包文件夹
				FileUtil.del(new File(pkgPath));
			} catch (Exception e) {
				log.error("清理文件时发生错误: {}", e.getMessage(), e);
			}
		}
	}

	/**
	 * 处理数据包的通用方法
	 */
	public List<JSONObject> processDataPackages(String sql,
												String phaseTreeId,
												JSONObject phaseModelObj,
												String packageType,
												BiConsumer<JSONObject, String> createPackageFunc) {
		List<JSONObject> successPackages = new ArrayList<>();
		JSONArray dpks = Util.postQuerySql(sql);
		log.info("查询到 {} 个数据包需要处理", dpks.size());

		for (int i = 0; i < dpks.size(); i++) {
			JSONObject dpk = dpks.getJSONObject(i);
			String dpkId = dpk.getStr("ID");
			String dpkName = dpk.getStr("NAME", "");

			//获取数据包下的所有pdf文件
			String pdfSql = "select * " +
					"from RESULTGATHER " +
					"where (FILE_FORMAT = 'pdf' or FILE_FORMAT = 'PDF') and FILEPATH is not null" +
					"  and NODECODE = " + dpkId;
			JSONArray files = Util.postQuerySql(pdfSql);

			// 检查是否有PDF文件且文件实际存在
			boolean hasValidPdf = false;
			if (files != null && !files.isEmpty()) {
				for (int j = 0; j < files.size(); j++) {
					JSONObject file = files.getJSONObject(j);
					String filePath = fileUploadPath + file.getStr("FILEPATH", "");
					if (FileUtil.exist(filePath)) {
						hasValidPdf = true;
						break;
					}
				}
			}

			// 只有在有实际存在的PDF文件时才创建数据包
			if (hasValidPdf) {
				try {
					// 先获取logId
					String pkgPath = generateUniquePath(i + 1, packageType);
					String logId = insertArchivePushLog(phaseTreeId, phaseModelObj, packageType,
							phaseModelObj.getStr("NODENAME") + "_" + dpk.getStr("NAME"), pkgPath);
					dpk.set("logId", logId);

					// 如果logId为null，表示该数据包已成功收集和推送，直接继续下一个
					if (logId == null) {
						continue;
					}

					// 创建目录并处理数据包
					log.info("创建数据包目录: {}, 数据包名称: {}, 类型: {}", pkgPath, dpk.getStr("NAME"), packageType);
					FileUtil.mkdir(pkgPath);

					try {
						createPackageFunc.accept(dpk, pkgPath);
						// 更新收集成功状态
						updateCollectStatus(logId, "SUCCESS", null,
								countPdfFiles(pkgPath), 1, countArchiveFiles(pkgPath));

						// 将成功收集的数据包信息添加到列表中
						JSONObject packageInfo = new JSONObject();
						packageInfo.set("pkgPath", pkgPath);
						packageInfo.set("logId", logId);
						successPackages.add(packageInfo);
					} catch (Exception e) {
						// 更新收集失败状态
						updateCollectStatus(logId, "FAILED", e.getMessage(), 0, 0, 0);
						log.error("处理数据包[{}]时发生错误", dpkName, e);
						throw e;
					}
				} catch (Exception e) {
					log.error("处理数据包[{}]时发生错误", dpkName, e);
					// 继续处理下一个数据包
				}
			} else {
				log.info("数据包[{}]没有有效的PDF文件，跳过生成", dpkName);
			}
		}
		return successPackages;
	}

	/**
	 * 查询数据包收集和推送状态
	 */
	public JSONArray queryPackageStatus(String phaseTreeId) {
		String sql = "SELECT a.*, " +
				"TO_CHAR(a.COLLECT_END_TIME, 'YYYY-MM-DD HH24:MI:SS') as COLLECT_END_TIME, " +
				"TO_CHAR(a.CREATE_TIME, 'YYYY-MM-DD HH24:MI:SS') as CREATE_TIME, " +
				"TO_CHAR(a.PUSH_END_TIME, 'YYYY-MM-DD HH24:MI:SS') as PUSH_END_TIME, " +
				"CASE " +
				"    WHEN a.COLLECT_STATUS = 'COLLECTING' THEN '收集中' " +
				"    WHEN a.COLLECT_STATUS = 'SUCCESS' AND a.PUSH_STATUS IS NULL THEN '收集完成' " +
				"    WHEN a.COLLECT_STATUS = 'FAILED' THEN '收集失败' " +
				"    WHEN a.PUSH_STATUS = 'PUSHING' THEN '推送中' " +
				"    WHEN a.PUSH_STATUS = 'SUCCESS' THEN '推送完成' " +
				"    WHEN a.PUSH_STATUS = 'FAILED' THEN '推送失败' " +
				"END as STATUS_TEXT, " +
				"CASE " +
				"    WHEN a.COLLECT_STATUS = 'COLLECTING' THEN 1 " +
				"    WHEN a.COLLECT_STATUS = 'SUCCESS' AND a.PUSH_STATUS IS NULL THEN 2 " +
				"    WHEN a.COLLECT_STATUS = 'FAILED' THEN 3 " +
				"    WHEN a.PUSH_STATUS = 'PUSHING' THEN 4 " +
				"    WHEN a.PUSH_STATUS = 'SUCCESS' THEN 5 " +
				"    WHEN a.PUSH_STATUS = 'FAILED' THEN 6 " +
				"END as STATUS_ORDER " +
				"FROM ARCHIVE_PUSH_LOG a " +
				"WHERE a.PHASE_TREE_ID = '" + phaseTreeId + "' " +
				"ORDER BY a.CREATE_TIME desc, a.COLLECT_END_TIME desc, a.PUSH_END_TIME desc";

		return Util.postQuerySql(sql);
	}

	/**
	 * 删除日志记录
	 */
	public void deleteLogRecord(String logId) {
		String sql = "DELETE FROM ARCHIVE_PUSH_LOG WHERE ID = '" + logId + "'";
		Util.postCommandSql(sql);
	}
}
