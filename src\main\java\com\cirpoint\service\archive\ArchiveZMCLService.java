package com.cirpoint.service.archive;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.cirpoint.service.OnlineConfirmService;
import com.cirpoint.util.ArchiveLogger;
import com.cirpoint.util.Util;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.cirpoint.util.CommonUtil.removeDecimalZero;

/**
 * 证明材料档案服务类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ArchiveZMCLService extends ArchiveBaseService {

	@Autowired
	private ArchivePackageService archivePackageService;

	@Autowired
	private OnlineConfirmService onlineConfirmService;

	// 证明材料案卷序号计数器
	private final AtomicInteger rollZMCLCounter = new AtomicInteger(1);

	/**
	 * 收集证明材料数据包
	 */
	public void collectZMCLPackage(String phaseTreeId, String pushUserFullName, JSONObject phaseModelObj, ArchiveLogger logger, GlobalRollSortManager globalSortManager) {
		try {
			logger.info("重置证明材料案卷序号计数器");
			// 重置案卷序号计数器
			rollZMCLCounter.set(1);

			logger.info("开始创建证明材料异步收集任务");
			// 创建两个异步任务
			CompletableFuture<Void> wdwFuture = CompletableFuture.runAsync(() -> {
				try {
					logger.info("开始收集外单位证明材料数据包");
					collectZMCL_WDWPackage(phaseTreeId, phaseModelObj, logger, globalSortManager);
					logger.info("外单位证明材料数据包收集完成");
				} catch (Exception e) {
					logger.error("收集外单位证明材料数据包失败", e);
					throw e;
				}
			});

			CompletableFuture<Void> bookFuture = CompletableFuture.runAsync(() -> {
				try {
					logger.info("开始收集证明材料四书数据包");
					collectZMCL_4BookPackage(phaseTreeId, pushUserFullName, phaseModelObj, logger, globalSortManager);
					logger.info("证明材料四书数据包收集完成");
				} catch (Exception e) {
					logger.error("收集证明材料四书数据包失败", e);
					throw e;
				}
			});

			// 等待所有任务完成
			try {
				logger.info("等待证明材料数据包收集任务完成");
				CompletableFuture.allOf(wdwFuture, bookFuture).join();
				logger.info("所有证明材料数据包收集任务已完成");
			} catch (Exception e) {
				logger.error("收集证明材料数据包过程中发生错误", e);
				throw new RuntimeException("收集证明材料数据包失败", e);
			}
		} catch (Exception e) {
			logger.error("收集证明材料数据包过程中发生错误", e);
			throw e;
		}
	}

	/**
	 * 收集外单位证明材料数据包
	 */
	private void collectZMCL_WDWPackage(String phaseTreeId, JSONObject phaseModelObj, ArchiveLogger logger, GlobalRollSortManager globalSortManager) {
		try {
			logger.info("开始查询外单位证明材料数据包信息");
			String sql = "select ID, REGEXP_REPLACE(NAME, '数据包$', '证明书、履历书') as NAME, CREATETIME, CODE " +
					"from DATAPKG_TREE_VIEW " +
					"where DIR_NAME like '%外单位证明书、履历书%' " +
					"  and REFTREEID in " +
					"      (select TREEID from DATAPACKAGETREE start with TREEID =  " + phaseTreeId + " connect by prior TREEID = PARENTID)";

			logger.info("=== 执行SQL查询 [collectZMCL_WDWPackage] ===");
			logger.info("SQL语句: " + sql);
			logger.info("参数: phaseTreeId = " + phaseTreeId);

			List<JSONObject> successPackages = archivePackageService.processDataPackages(sql, phaseTreeId, phaseModelObj, "ZMCL", (dpk, pkgPath) -> {
				try {
					logger.info(String.format("开始处理外单位证明材料数据包: %s, 路径: %s", dpk.getStr("NAME"), pkgPath));
					createPackage(dpk, pkgPath, phaseModelObj, logger, globalSortManager);
					logger.info(String.format("外单位证明材料数据包 %s 处理完成", dpk.getStr("NAME")));
				} catch (Exception e) {
					logger.error(String.format("处理外单位证明材料数据包 %s 失败", dpk.getStr("NAME")), e);
					throw new RuntimeException(e);
				}
			});

			if (successPackages.isEmpty()) {
				logger.info("没有找到需要处理的外单位证明材料数据包");
				return;
			}

			logger.info(String.format("成功处理 %d 个外单位证明材料数据包，开始异步推送", successPackages.size()));

			@SuppressWarnings("unchecked")
			CompletableFuture<Void>[] futures = new CompletableFuture[successPackages.size()];
			for (int i = 0; i < successPackages.size(); i++) {
				final JSONObject packageInfo = successPackages.get(i);
				futures[i] = CompletableFuture.runAsync(() -> {
					try {
						String pkgPath = packageInfo.getStr("pkgPath");
						String logId = packageInfo.getStr("logId");
						logger.info(String.format("开始推送外单位证明材料数据包，路径: %s, 日志ID: %s", pkgPath, logId));
						archivePackageService.zipAndPushPackage(pkgPath, logId);
						logger.info(String.format("外单位证明材料数据包推送完成，日志ID: %s", logId));
					} catch (Exception e) {
						logger.error("推送外单位证明材料数据包失败", e);
						throw new RuntimeException(e);
					}
				});
			}
			CompletableFuture.allOf(futures)
					.exceptionally(ex -> {
						logger.error("部分外单位证明材料数据包推送失败", ex);
						return null;
					});
			logger.info("外单位证明材料数据包异步推送任务已创建");
		} catch (Exception e) {
			logger.error("收集外单位证明材料数据包过程中发生错误", e);
			throw e;
		}
	}

	/**
	 * 收集证明材料四书的数据包
	 */
	private void collectZMCL_4BookPackage(String phaseTreeId, String pushUserFullName, JSONObject phaseModelObj, ArchiveLogger logger, GlobalRollSortManager globalSortManager) {
		try {
			logger.info("开始生成证明材料四书数据包路径");
			String packageType = "ZMCL";
			String pkgPath = generateUniquePath(1, packageType);
			String logId = insertArchivePushLog(phaseTreeId, phaseModelObj, packageType,
					phaseModelObj.getStr("NODENAME") + "_证明材料四书", pkgPath);

			if (logId == null) {
				logger.info("证明材料四书数据包已存在且成功推送，跳过处理");
				return;
			}

			String modelName = phaseModelObj.getStr("MODEL_NAME");
			logger.info(String.format("开始查询型号[%s]的四书节点", modelName));
			String sql = "SELECT c.* FROM LAUNCH_CONFIRM c INNER JOIN LAUNCH_CONFIRM p ON c.PID = p.ID WHERE c.TYPE = 'project' AND c.NAME = '" + modelName + "' AND p.NAME LIKE '%承诺书%'";

			logger.info("=== 执行SQL查询 [collectZMCL_4BookPackage] ===");
			logger.info("SQL语句: " + sql);

			JSONArray projectNodes = Util.postQuerySql(sql);

			// 如果没有找到项目节点，直接返回
			if (projectNodes.isEmpty()) {
				logger.info(String.format("型号[%s]没有找到在发射场的四书节点，跳过生成证明材料四书数据包", modelName));
				return;
			}

			String nodeName = phaseModelObj.getStr("NODENAME");
			logger.info("开始创建证明材料四书数据包XML文件");

			try {
				createPackageXml(pkgPath, (roll, archive) -> {
					try {
						int rollNo = rollZMCLCounter.getAndIncrement();
						int rollSort = globalSortManager.getNextSort();
						String rollId = String.format("0802(%s)ZMCL-%03d", nodeName, rollNo);
						logger.info(String.format("创建证明材料四书案卷XML，案卷号: %s, 排序号: %d", rollId, rollSort));
						createRollXml(phaseTreeId + "_ZMCL_4Book", "证明材料四书案卷", rollId, pushUserFullName,
								DateUtil.today(), String.valueOf(rollNo), pkgPath, nodeName + "_roll_ZMCL_4Book", nodeName, roll, rollSort);

						String archiveFolderPath = pkgPath + "4Book\\";
						logger.info(String.format("创建证明材料四书档案目录: %s, 数据包名称: %s", archiveFolderPath, nodeName + "_证明材料四书"));
						FileUtil.mkdir(archiveFolderPath);

						String id = projectNodes.getJSONObject(0).getStr("ID");

						//父节点中的名称包含承诺书，按名称排序并生成档案序号
						logger.info("开始查询A表子节点");
						String sql3 = "SELECT t.*, ROW_NUMBER() OVER (ORDER BY NAME) as ARCH_XH " +
								"FROM LAUNCH_CONFIRM t WHERE PID = " + id + " ORDER BY NAME";

						logger.info("=== 执行SQL查询 [collectZMCL_4BookPackage - A表子节点] ===");
						logger.info("SQL语句: " + sql3);
						logger.info("参数: id = " + id);

						JSONArray aNodes = Util.postQuerySql(sql3);
						logger.info(String.format("找到 %d 个A表子节点，已按名称排序并分配档案序号", aNodes.size()));

						// 创建所有导出PDF任务的Future列表
						List<CompletableFuture<Void>> pdfFutures = new ArrayList<>();

						for (int i = 0; i < aNodes.size(); i++) {
							JSONObject aNode = aNodes.getJSONObject(i);
							String aNodeId = aNode.getStr("ID");
							String aNodeName = aNode.getStr("NAME");
							String archXH = removeDecimalZero(aNode.getStr("ARCH_XH", "1"));
							int archSort = i + 1; // 档案排序号从1开始

							// 为每个PDF导出创建一个异步任务
							CompletableFuture<Void> pdfFuture = CompletableFuture.runAsync(() -> {
								try {
									logger.info(String.format("开始导出节点[%s]的PDF文件", aNodeName));
									// 为每个任务生成唯一的临时目录
									String uniqueTempPath = tempPath + UUID.randomUUID() + "\\";
									logger.info(String.format("创建临时目录: %s", uniqueTempPath));
									FileUtil.mkdir(uniqueTempPath);

									//生成pdf文件
									JSONObject exportMorePdfRes = onlineConfirmService.exportMorePdf(
											aNodeId, id, "Thing.Fn.LaunchOnlineConfirm",
											uniqueTempPath, pdfFontPath, false);
									if (exportMorePdfRes.getBool("success")) {
										String srcFilePath = exportMorePdfRes.getStr("data");
										String fileName = aNodeName + ".pdf";
										String innerId = removeDecimalZero(aNodeId) + "_ZMCL_4Book";
										logger.info(String.format("创建PDF文件档案XML，文件名: %s, 序号: %s, 排序号: %d", fileName, archXH, archSort));
										createConfirmArch(srcFilePath, archiveFolderPath, fileName, innerId, archXH, phaseModelObj, archive, pkgPath, rollId, archSort);
										// 清理临时目录
										FileUtil.del(uniqueTempPath);
										logger.info(String.format("节点[%s]的PDF文件处理完成", aNodeName));
									} else {
										logger.info(String.format("导出节点[%s]的PDF文件失败: %s", aNodeName, exportMorePdfRes.getStr("msg")));
									}
								} catch (Exception e) {
									logger.error(String.format("处理节点[%s]的PDF文件失败", aNodeName), e);
									throw e;
								}
							});
							pdfFutures.add(pdfFuture);
						}

						// 等待所有PDF导出任务完成
						try {
							logger.info("等待所有PDF导出任务完成");
							CompletableFuture.allOf(pdfFutures.toArray(new CompletableFuture[0])).join();
							logger.info("所有PDF导出任务已完成");
						} catch (Exception e) {
							logger.error("等待PDF导出任务完成时发生错误", e);
							throw e;
						}
					} catch (Exception e) {
						logger.error("创建证明材料四书数据包XML文件失败", e);
						throw e;
					}
				});

				updateCollectStatus(logId, "SUCCESS", null,
						countPdfFiles(pkgPath), 1, countArchiveFiles(pkgPath));
				logger.info("证明材料四书数据包收集完成，开始推送");
				archivePackageService.zipAndPushPackage(pkgPath, logId);
				logger.info("证明材料四书数据包推送完成");
			} catch (Exception e) {
				updateCollectStatus(logId, "FAILED", e.getMessage(), 0, 0, 0);
				logger.error("证明材料四书数据包处理失败", e);
				throw e;
			}
		} catch (Exception e) {
			logger.error("收集证明材料四书数据包过程中发生错误", e);
			throw e;
		}
	}

	/**
	 * 创建数据包
	 */
	private void createPackage(JSONObject dpk, String pkgPath, JSONObject phaseModelObj, ArchiveLogger logger, GlobalRollSortManager globalSortManager) {
		createPackageXml(pkgPath, (roll, archive) ->
				createRoll(dpk, roll, archive, pkgPath, phaseModelObj, logger, globalSortManager));
	}

	/**
	 * 创建案卷
	 */
	private void createRoll(JSONObject dpk, Element rollEl, Element archiveEl,
							String pkgPath, JSONObject phaseModelObj, ArchiveLogger logger, GlobalRollSortManager globalSortManager) {
		String dpkId = removeDecimalZero(dpk.getStr("ID"));
		String dpkName = dpk.getStr("NAME", "");
		String dpkCode = dpk.getStr("CODE", "");
		String creator = dpk.getStr("USER_FULLNAME", "");
		String createTime = dpk.getStr("CREATETIME", "");
		String phaseNodeName = phaseModelObj.getStr("NODENAME");
		String archFilePath = pkgPath + dpkCode + "\\";
		FileUtil.mkdir(pkgPath);

		String innerId = dpkId + "_" + dpkCode;
		String rollId = String.format("0802(%s)ZMCL-%03d", phaseNodeName, rollZMCLCounter.getAndIncrement());
		String rollDocName = "roll_" + dpkCode;
		String rollBuildTime = DateUtil.format(DateUtil.parse(createTime), "yyyy-MM-dd");
		int rollSort = globalSortManager.getNextSort();

		logger.info(String.format("创建证明材料案卷XML，案卷号: %s, 排序号: %d", rollId, rollSort));
		createRollXml(innerId, dpkName, rollId, creator, rollBuildTime, dpkId,
				pkgPath, rollDocName, phaseNodeName, rollEl, rollSort);

		//获取数据包下的所有pdf文件，按题名排序并生成档案序号
		String sql = "SELECT t.*, ROW_NUMBER() OVER (ORDER BY FILE_NAME) as ARCH_XH " +
				"FROM RESULTGATHER t " +
				"WHERE (FILE_FORMAT = 'pdf' OR FILE_FORMAT = 'PDF') AND FILEPATH IS NOT NULL " +
				"AND NODECODE = " + dpkId + " " +
				"ORDER BY FILE_NAME";

		logger.info("=== 执行SQL查询 [createRoll] ===");
		logger.info("SQL语句: " + sql);
		logger.info("参数: dpkId = " + dpkId);
		logger.info("参数: dpkName = " + dpkName);
		logger.info("参数: dpkCode = " + dpkCode);

		JSONArray files = Util.postQuerySql(sql);
		logger.info("查询到 {} 个文件，已按题名排序并分配档案序号", files.size());

		// 为档案分配独立序号，跳过不存在的文件
		int archSort = 1;
		for (int i = 0; i < files.size(); i++) {
			JSONObject file = files.getJSONObject(i);
			String srcFilePath = fileUploadPath + file.getStr("FILEPATH", "");
			// 只为存在的文件分配序号
			if (FileUtil.exist(srcFilePath)) {
				createArch(file, archiveEl, archFilePath, phaseModelObj, rollId, logger, archSort);
				archSort++;
			} else {
				logger.info("文件不存在，跳过: " + srcFilePath);
			}
		}
	}

	/**
	 * 创建档案XML和PDF文件
	 */
	private void createArch(JSONObject file, Element archiveEl, String archFilePath, JSONObject phaseModelObj, String rollNo, ArchiveLogger logger, int archSort) {
		String id = file.getStr("ID");
		String tableName = file.getStr("TABLENAME", "");
		String innerId = tableName + "_" + id;
		String archTitle = file.getStr("FILE_NAME", ""); //题名
		String archXH = removeDecimalZero(file.getStr("ARCH_XH", "1")); //档案序号，从SQL查询结果获取并去除小数
		String secretId = getSecretId(file.getStr("SECURITY_LEVEL", "内部")); //密级
		String editDate = file.getStr("CREATE_TIMESTAMP", "").substring(0, 10); //编制日期 yyyy-MM-dd
		String srcFilePath = fileUploadPath + file.getStr("FILEPATH", ""); //文件路径
		
		logger.info("处理档案: 题名={}, 序号={}, 排序号={}", archTitle, archXH, archSort);

		//判断文件是否存在
		if (FileUtil.exist(srcFilePath)) {
			String fileName = FileNameUtil.getName(srcFilePath) + ".pdf";
			createArchXml(srcFilePath, archFilePath, fileName, innerId, archXH, archTitle, secretId, editDate, rollNo,
					phaseModelObj, archiveEl, FileUtil.getParent(archFilePath, 1), archSort);
		}
	}

	/**
	 * 创建确认表档案XML和PDF文件
	 */
	private void createConfirmArch(String srcFilePath, String archiveFolderPath, String fileName,
								   String innerId, String archXH, JSONObject phaseModelObj, Element archiveEl, String pkgPath, String rollId, int archSort) {
		createArchXml(srcFilePath, archiveFolderPath, fileName, innerId, archXH, fileName,
				getSecretId(""), DateUtil.today(), rollId, phaseModelObj, archiveEl, pkgPath, archSort);
	}
}
