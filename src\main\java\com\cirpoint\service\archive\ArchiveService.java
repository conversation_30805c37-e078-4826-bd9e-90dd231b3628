package com.cirpoint.service.archive;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.cirpoint.service.OnlineConfirmService;
import com.cirpoint.service.QualityReportService;
import com.cirpoint.util.ArchiveLogger;
import com.cirpoint.util.Util;
import java.io.File;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 档案服务门面类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ArchiveService {

	@Autowired
	private ArchivePackageService archivePackageService;

	@Autowired
	private ArchiveYSJLService archiveYSJLService;

	@Autowired
	private ArchiveZMCLService archiveZMCLService;

	@Autowired
	private ArchiveQRBService archiveQRBService;

	@Autowired
	private ArchivePhotoService archivePhotoService;

	@Autowired
	private QualityReportService qualityReportService;

	@Autowired
	private OnlineConfirmService onlineConfirmService;

	/**
	 * 推送数据包文件到档案系统
	 */
	public void pushFile(File zipFile) throws Exception {
		archivePackageService.pushFile(zipFile);
	}

	/**
	 * 检查是否有正在进行的收集任务
	 */
	public boolean hasRunningTask(String phaseTreeId) {
		return archivePackageService.hasRunningTask(phaseTreeId);
	}

	/**
	 * 收集数据包
	 */
	public void collectPackage(String phaseTreeId, String pushUserName, String pushUserFullname) {
		ArchiveLogger baseLogger = null;
		ArchiveLogger ysjlLogger;
		ArchiveLogger zmclLogger;
		ArchiveLogger qrbLogger;
		ArchiveLogger photoLogger;
		try {
			// 检查是否有正在进行的任务
			if (hasRunningTask(phaseTreeId)) {
				throw new RuntimeException("该阶段正在进行数据包收集，请等待当前任务完成后再试");
			}

			//查询阶段的型号名称
			String sql = "select * from PHASE_MODEL where TREEID = " + phaseTreeId;

			log.info("=== 执行SQL查询 [collectPackage - 查询阶段型号名称] ===");
			log.info("SQL语句: {}", sql);
			log.info("参数: phaseTreeId = {}", phaseTreeId);

			JSONArray phaseModels = Util.postQuerySql(sql);
			if (!phaseModels.isEmpty()) {
				JSONObject phaseModelObj = phaseModels.getJSONObject(0);
				phaseModelObj.set("pushUserName", pushUserName);
				phaseModelObj.set("pushUserFullname", pushUserFullname);
				// 初始化基础日志目录
				baseLogger = new ArchiveLogger(
						phaseModelObj.getStr("MODEL_NAME"),
						phaseModelObj.getStr("PHASE_NAME"),
						pushUserFullname,
						"main"
				);
				ysjlLogger = new ArchiveLogger(phaseModelObj.getStr("MODEL_NAME"), phaseModelObj.getStr("PHASE_NAME"), pushUserFullname, "原始记录");
				zmclLogger = new ArchiveLogger(phaseModelObj.getStr("MODEL_NAME"), phaseModelObj.getStr("PHASE_NAME"), pushUserFullname, "证明材料");
				qrbLogger = new ArchiveLogger(phaseModelObj.getStr("MODEL_NAME"), phaseModelObj.getStr("PHASE_NAME"), pushUserFullname, "确认表");
				photoLogger = new ArchiveLogger(phaseModelObj.getStr("MODEL_NAME"), phaseModelObj.getStr("PHASE_NAME"), pushUserFullname, "照片");

				baseLogger.info("开始收集数据包任务");
				baseLogger.info(String.format("阶段树ID: %s, 推送用户: %s", phaseTreeId, pushUserFullname));

				// 删除未成功的数据包记录
				baseLogger.info("开始清理未成功的数据包记录");
				String deleteSql = "DELETE FROM ARCHIVE_PUSH_LOG WHERE PHASE_TREE_ID = '" + phaseTreeId + "' " +
						"AND ((COLLECT_STATUS = 'FAILED') " +
						"OR (COLLECT_STATUS = 'SUCCESS' AND PUSH_STATUS = 'FAILED') " +
						"OR (COLLECT_STATUS = 'SUCCESS' AND PUSH_STATUS IS NULL))";

				baseLogger.info("=== 执行SQL语句 [collectPackage - 清理未成功的数据包记录] ===");
				baseLogger.info("SQL语句: " + deleteSql);
				baseLogger.info("参数: phaseTreeId = " + phaseTreeId);

				Util.postCommandSql(deleteSql);
				baseLogger.info("清理未成功的数据包记录完成");

				// 创建四个异步任务
				baseLogger.info("开始创建异步收集任务");

				// 创建final的logger引用
				final ArchiveLogger finalYsjlLogger = ysjlLogger;
				final ArchiveLogger finalZmclLogger = zmclLogger;
				final ArchiveLogger finalQrbLogger = qrbLogger;
				final ArchiveLogger finalPhotoLogger = photoLogger;

				// 创建全局案卷序号管理器
				ArchiveBaseService.GlobalRollSortManager globalSortManager = new ArchiveBaseService.GlobalRollSortManager();
				baseLogger.info("创建全局案卷序号管理器，开始按服务优先级分配案卷序号");

				// 按照服务优先级顺序执行，确保案卷序号正确分配
				// 1. 证明材料服务（优先级最高）
				try {
					finalZmclLogger.info("开始收集证明材料数据包（优先级1）");
					archiveZMCLService.collectZMCLPackage(phaseTreeId, pushUserFullname, phaseModelObj, finalZmclLogger, globalSortManager);
					finalZmclLogger.info("证明材料数据包收集完成");
				} catch (Exception e) {
					finalZmclLogger.error("收集证明材料数据包失败", e);
					throw e;
				}

				// 2. 原始记录服务（优先级2）
				try {
					finalYsjlLogger.info("开始收集原始记录数据包（优先级2）");
					archiveYSJLService.collectYSJLPackage(phaseTreeId, phaseModelObj, finalYsjlLogger, globalSortManager);
					finalYsjlLogger.info("原始记录数据包收集完成");
				} catch (Exception e) {
					finalYsjlLogger.error("收集原始记录数据包失败", e);
					throw e;
				}

				CompletableFuture<Void> qrbFuture = CompletableFuture.runAsync(() -> {
					try {
						finalQrbLogger.info("开始收集确认表数据包");
						archiveQRBService.collectQRBPackage(phaseTreeId, pushUserName, pushUserFullname, phaseModelObj, finalQrbLogger);
						finalQrbLogger.info("确认表数据包收集完成");
					} catch (Exception e) {
						finalQrbLogger.error("收集确认表数据包失败", e);
						throw e;
					}
				});

				CompletableFuture<Void> photoFuture = CompletableFuture.runAsync(() -> {
					try {
						finalPhotoLogger.info("开始收集照片数据包");
						archivePhotoService.collectPhotoPackage(phaseTreeId, pushUserFullname, phaseModelObj, finalPhotoLogger);
						finalPhotoLogger.info("照片数据包收集完成");
					} catch (Exception e) {
						finalPhotoLogger.error("收集照片数据包失败", e);
						throw e;
					}
				});

				// 等待所有任务完成
				try {
					baseLogger.info("等待所有数据包收集任务完成");
					CompletableFuture.allOf(ysjlFuture, zmclFuture, qrbFuture, photoFuture).join();
					baseLogger.info("所有数据包收集任务已完成");
				} catch (Exception e) {
					baseLogger.error("收集数据包过程中发生错误", e);
					throw new RuntimeException("收集数据包失败", e);
				}
			} else {
				throw new RuntimeException("未找到阶段信息: " + phaseTreeId);
			}
		} catch (Exception e) {
			if (baseLogger != null) {
				baseLogger.error("数据包收集任务失败", e);
			}
			throw e;
		} finally {
			if (baseLogger != null) {
				baseLogger.info("数据包收集任务结束，日志文件路径: " + baseLogger.getLogFilePath());
			}
		}
	}

	/**
	 * 查询数据包收集和推送状态
	 */
	public JSONArray queryPackageStatus(String phaseTreeId) {
		return archivePackageService.queryPackageStatus(phaseTreeId);
	}

	/**
	 * 删除日志记录
	 */
	public void deleteLogRecord(String logId) {
		archivePackageService.deleteLogRecord(logId);
	}
}
