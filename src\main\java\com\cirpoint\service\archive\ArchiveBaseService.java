package com.cirpoint.service.archive;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cirpoint.service.ApplicationConfig;
import com.cirpoint.util.Util;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfReader;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.XMLWriter;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * 档案基础服务类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ArchiveBaseService extends ApplicationConfig {

	public static final String ROLL_CLASS_ID = "ZhiLiangRoll";
	public static final String ARCH_CLASS_ID = "ZhiLiangArch";

	/**
	 * 全局案卷序号管理器
	 */
	public static class GlobalRollSortManager {
		private int currentSort = 1;

		public synchronized int getNextSort() {
			return currentSort++;
		}

		public synchronized void reset() {
			currentSort = 1;
		}
	}


	protected JSONObject getPackageXmlJson() {
		return Util.postTwxForObject("Thing.Fn.ListData", "GetStringPropertyValue",
						JSONUtil.createObj().set("propertyName", "packageXml"))
				.getJSONArray("rows")
				.getJSONObject(0)
				.getJSONObject("result");
	}

	protected JSONObject getAxisJson() {
		return Util.postTwxForObject("Thing.Fn.ListData", "GetStringPropertyValue",
						JSONUtil.createObj().set("propertyName", "axisParam"))
				.getJSONArray("rows")
				.getJSONObject(0)
				.getJSONObject("result");
	}

	/**
	 * 生成唯一的数据包路径
	 */
	protected String generateUniquePath(int pathCounter, String type) {
		return pushTempPath + type + "_" + System.currentTimeMillis() + "_" + pathCounter + "\\";
	}

	/**
	 * 通过密级中文名称获取密级代号
	 */
	protected String getSecretId(String secretName) {
		if (cn.hutool.core.util.StrUtil.isEmpty(secretName)) {
			return "20";
		}
		switch (secretName) {
			case "公开":
				return "10";
			case "秘密":
				return "30";
			case "机密":
				return "40";
			case "内部":
			default:
				return "20";
		}
	}

	/**
	 * 创建package.xml公共方法
	 */
	protected void createPackageXml(String pkgPath, CreateFilesCallback createFilesCallback) {
		Document doc = DocumentHelper.createDocument();
		Element packageEl = doc.addElement("package");
		JSONObject xmlJson = getPackageXmlJson();
		packageEl.addElement("app_id").addText(xmlJson.getStr("app_id"));
		packageEl.addElement("app_name").addText(xmlJson.getStr("app_name"));
		packageEl.addElement("org_name").addText(xmlJson.getStr("org_name"));
		packageEl.addElement("source_id").addText(xmlJson.getStr("source_id"));
		packageEl.addElement("file_type").addText(xmlJson.getStr("file_type"));
		Element files = packageEl.addElement("files");
		Element archive = files.addElement("archive");
		Element roll = files.addElement("roll");

		// 调用回调函数处理files节点
		createFilesCallback.createFiles(roll, archive);

		writeDocumentToXml(doc, pkgPath, "package");
	}

	/**
	 * 创建案卷XML文件的通用方法
	 */
	protected void createRollXml(String innerId, String rollName, String rollId,
								 String rollBuilderName, String rollBuildTime, String rollXH,
								 String pkgPath, String rollDocName, String xinghao, Element rollEl, int sort) {
		// 创建案卷的xml文档
		Document rollDoc = DocumentHelper.createDocument();
		// obj: 根节点，表示一个案卷对象
		Element objEl = rollDoc.addElement("obj");
		// innerId: 案卷的唯一标识
		objEl.addElement("innerId").addText(innerId);
		// classId: 案卷类型，固定值为航天产品案卷
		objEl.addElement("classId").addText(ROLL_CLASS_ID);
		// attrs: 案卷属性信息节点
		Element attrsEl = objEl.addElement("attrs");
		// rollName: 案卷名称 xinghao+rollName
		attrsEl.addElement("rollName").addText(xinghao + "_" + rollName);
		// rollId: 案卷号
		attrsEl.addElement("rollId").addText(rollId);
		// secretId: 密级，默认20表示内部
		attrsEl.addElement("secretId").addText("20");
		// retentionPeriod: 保管期限，默认30年
		attrsEl.addElement("retentionPeriod").addText("30");
		// rollBuilderName: 立卷人
		attrsEl.addElement("rollBuilderName").addText(rollBuilderName);
		log.info("立卷日期: {}", rollBuildTime);
		// rollBuildTime: 立卷日期
		attrsEl.addElement("rollBuildTime").addText(rollBuildTime);
		// rollXH: 案卷序号
		attrsEl.addElement("rollXH").addText(rollXH);
		// xinghao: 型号
		attrsEl.addElement("xinghao").addText(xinghao);
		// sort: 排序字段
		attrsEl.addElement("sort").addText(String.valueOf(sort));

		// 保存案卷xml文件
		writeDocumentToXml(rollDoc, pkgPath, rollDocName);
		rollEl.addElement("path").addText(rollDocName + ".xml");
	}

	/**
	 * 创建档案XML文件的通用方法
	 */
	protected void createArchXml(String srcFilePath, String archiveFolderPath, String fileName,
								 String innerId, String archXH, String archTitle, String secretId, String editDate, String rollNo,
								 JSONObject phaseModelObj, Element archiveEl, String pkgPath, int sort) {
		archTitle = phaseModelObj.getStr("NODENAME") + "_" + archTitle;
		String archFilePath = archiveFolderPath + fileName;
		//将pdf文件复制到档案路径下
		FileUtil.copy(srcFilePath, archFilePath, true);
		//创建档案xml文件
		int pageCount = getPdfPageCount(srcFilePath); //页数 需要计算
		//创建档案的xml文件
		Document doc = DocumentHelper.createDocument();
		// obj: 根节点，表示一个档案对象
		Element objEl = doc.addElement("obj");
		// innerId: 档案的唯一标识，
		objEl.addElement("innerId").addText(innerId);
		// classId: 档案类型，由档案系统提供
		objEl.addElement("classId").addText(ARCH_CLASS_ID);
		// attrs: 档案属性信息节点
		Element attrsEl = objEl.addElement("attrs");
		// archTitle: 档案题名，使用文件名称
		attrsEl.addElement("archTitle").addText(archTitle);
		// secretId: 密级，从文件安全级别获取
		attrsEl.addElement("secretId").addText(secretId);
		// editDate: 编制日期
		attrsEl.addElement("editDate").addText(editDate);
		// pageCount: 文件页数，通过PDF计算获得
		attrsEl.addElement("pageCount").addText(Convert.toStr(pageCount));
		// filingOrgName: 归档单位名称
		attrsEl.addElement("filingOrgName").addText("质量技术处");
		// filingTime: 归档时间，使用当前日期
		attrsEl.addElement("filingTime").addText(DateUtil.today());
		// TYPERIGHT: 型号代号
		attrsEl.addElement("TYPERIGHT").addText(phaseModelObj.getStr("MODEL_NAME"));
		// CPJD: 产品阶段
		attrsEl.addElement("CPJD").addText(phaseModelObj.getStr("PHASE_NAME"));
		// archXH: 档案序号
		attrsEl.addElement("archXH").addText(archXH);
		// rollNo: 案卷号
		attrsEl.addElement("rollNo").addText(rollNo);
		// sort: 排序字段
		attrsEl.addElement("sort").addText(String.valueOf(sort));

		// files: 文件信息节点
		Element filesEl = objEl.addElement("files");
		// file: 具体文件节点
		Element fileEl = filesEl.addElement("file");
		// attrs: 文件属性节点
		Element fileAttrsEl = fileEl.addElement("attrs");
		fileAttrsEl.addElement("path").addText(fileName);
		fileAttrsEl.addElement("fileSize").addText(Convert.toStr(FileUtil.size(FileUtil.file(srcFilePath))));
		// 标准化fileName，确保与path字段后缀一致
		String standardizedFileName = standardizeFileName(archTitle, fileName);
		fileAttrsEl.addElement("fileName").addText(standardizedFileName);
		// 保存档案xml文件
		writeDocumentToXml(doc, archiveFolderPath, "arch_" + innerId);

		archiveEl.addElement("path").addText(FileUtil.subPath(pkgPath, archiveFolderPath) + "arch_" + innerId + ".xml");
	}

	/**
	 * 将Document对象写入到xml文件中
	 */
	protected void writeDocumentToXml(Document doc, String filePath, String fileName) {
		try {
			// 创建文件夹（如果不存在）
			FileUtil.mkdir(filePath);
			// 获取完整的文件路径
			String fullPath = filePath + fileName + ".xml";
			// 将document对象写入文件，使用UTF-8编码，并且格式化输出
			OutputFormat format = OutputFormat.createPrettyPrint();
			format.setEncoding("UTF-8");
			XMLWriter writer = new XMLWriter(
					Files.newOutputStream(Paths.get(fullPath)), format);
			writer.write(doc);
			writer.close();
			log.info("XML文件已保存到: {}", fullPath);
		} catch (Exception e) {
			log.error("写入XML文件失败: {}", e.getMessage(), e);
		}
	}

	/**
	 * 标准化档案文件名，确保与path字段后缀一致
	 * 
	 * @param archTitle 档案题名
	 * @param pathFileName 路径文件名（含后缀）
	 * @return 标准化后的fileName
	 */
	protected String standardizeFileName(String archTitle, String pathFileName) {
		try {
			// 参数校验
			if (cn.hutool.core.util.StrUtil.isEmpty(pathFileName)) {
				log.warn("pathFileName为空，返回原archTitle: {}", archTitle);
				return cn.hutool.core.util.StrUtil.isEmpty(archTitle) ? "" : archTitle;
			}
			
			// 如果archTitle为空，使用pathFileName的主文件名
			if (cn.hutool.core.util.StrUtil.isEmpty(archTitle)) {
				String result = FileNameUtil.mainName(pathFileName);
				log.debug("archTitle为空，使用pathFileName主文件名: {}", result);
				return result;
			}
			
			// 提取path字段的文件后缀
			String pathExtension = FileNameUtil.extName(pathFileName);
			
			// 如果path没有后缀，直接返回archTitle
			if (cn.hutool.core.util.StrUtil.isEmpty(pathExtension)) {
				log.debug("pathFileName无后缀，直接返回archTitle: {}", archTitle);
				return archTitle;
			}
			
			// 检查archTitle是否已包含后缀
			String archExtension = FileNameUtil.extName(archTitle);
			
			// 如果archTitle没有后缀，添加path的后缀
			if (cn.hutool.core.util.StrUtil.isEmpty(archExtension)) {
				String result = archTitle + "." + pathExtension;
				log.debug("为archTitle添加后缀: {} -> {}", archTitle, result);
				return result;
			}
			
			// 如果archTitle已有后缀但与path不一致，替换为path的后缀
			if (!pathExtension.equalsIgnoreCase(archExtension)) {
				String mainName = FileNameUtil.mainName(archTitle);
				String result = mainName + "." + pathExtension;
				log.debug("替换archTitle后缀: {} -> {}", archTitle, result);
				return result;
			}
			
			// 如果后缀一致，直接返回archTitle
			log.debug("archTitle后缀已正确，无需修改: {}", archTitle);
			return archTitle;
			
		} catch (Exception e) {
			log.error("标准化fileName时发生错误，archTitle: {}, pathFileName: {}", archTitle, pathFileName, e);
			// 异常时返回原archTitle或合理默认值
			return cn.hutool.core.util.StrUtil.isEmpty(archTitle) ? 
				FileNameUtil.getName(pathFileName) : archTitle;
		}
	}

	/**
	 * 通过pdf的文件地址获取pdf的实际页数
	 */
	protected int getPdfPageCount(String filePath) {
		try (PdfReader reader = new PdfReader(filePath);
			 PdfDocument pdfDoc = new PdfDocument(reader)) {
			return pdfDoc.getNumberOfPages();
		} catch (IOException e) {
			System.err.println("读取PDF文件时发生错误: " + e.getMessage());
			return -1; // 返回-1表示发生错误
		}
	}

	/**
	 * 统计PDF文件数量
	 */
	protected int countPdfFiles(String path) {
		return FileUtil.loopFiles(path, file ->
				file.getName().toLowerCase().endsWith(".pdf")).size();
	}

	/**
	 * 统计档案文件数量
	 */
	protected int countArchiveFiles(String path) {
		return FileUtil.loopFiles(path, file ->
				file.getName().toLowerCase().startsWith("arch_") &&
						file.getName().toLowerCase().endsWith(".xml")).size();
	}

	/**
	 * 记录数据包信息
	 * 如果数据包已存在且成功收集推送，则返回null表示跳过
	 * 如果数据包不存在或需要重新收集，则返回logId
	 */
	protected String insertArchivePushLog(String phaseTreeId, JSONObject phaseModelObj, String packageType,
										  String packageName, String packagePath) {
		// 先查询是否存在相同的数据包记录
		String querySql = "SELECT ID, COLLECT_STATUS, PUSH_STATUS FROM ARCHIVE_PUSH_LOG " +
				"WHERE PHASE_TREE_ID = '" + phaseTreeId + "' " +
				"AND PACKAGE_TYPE = '" + packageType + "' " +
				"AND PACKAGE_NAME = '" + packageName + "'";

		JSONArray existingLogs = Util.postQuerySql(querySql);

		if (!existingLogs.isEmpty()) {
			JSONObject existingLog = existingLogs.getJSONObject(0);
			String collectStatus = existingLog.getStr("COLLECT_STATUS");
			String pushStatus = existingLog.getStr("PUSH_STATUS");
			String existingId = existingLog.getStr("ID");

			// 如果已经成功收集和推送，则跳过
			if ("SUCCESS".equals(collectStatus) && "SUCCESS".equals(pushStatus)) {
				log.info("数据包[{}]已成功收集和推送，跳过处理", packageName);
				return null;
			}

			// 更新现有记录
			String updateSql = "UPDATE ARCHIVE_PUSH_LOG SET " +
					"COLLECT_STATUS = 'COLLECTING', " +
					"COLLECT_MESSAGE = NULL, " +
					"COLLECT_END_TIME = NULL, " +
					"PUSH_STATUS = NULL, " +
					"PUSH_MESSAGE = NULL, " +
					"PUSH_END_TIME = NULL, " +
					"CREATE_TIME = NULL, " +
					"ZIP_PATH = NULL, " +
					"PACKAGE_PATH = '" + packagePath + "', " +
					"PDF_COUNT = 0, " +
					"ROLL_COUNT = 0, " +
					"ARCHIVE_COUNT = 0 " +
					"WHERE ID = '" + existingId + "'";

			Util.postCommandSql(updateSql);
			return existingId;
		}

		// 如果不存在，则插入新记录
		String id = UUID.randomUUID().toString().replace("-", "");
		String insertSql = "INSERT INTO ARCHIVE_PUSH_LOG (ID, PHASE_TREE_ID, PHASE_NAME, MODEL_NAME, PACKAGE_TYPE, " +
				"PACKAGE_NAME, PACKAGE_PATH, CREATOR, COLLECT_STATUS) VALUES ('" + id + "', '" + phaseTreeId + "', '" +
				phaseModelObj.getStr("PHASE_NAME") + "', '" + phaseModelObj.getStr("MODEL_NAME") + "', '" +
				packageType + "', '" + packageName + "', '" + packagePath + "', '" + phaseModelObj.getStr("pushUserFullname") + "', 'COLLECTING')";

		Util.postCommandSql(insertSql);
		return id;
	}

	/**
	 * 更新收集状态
	 */
	protected void updateCollectStatus(String logId, String status, String message, int pdfCount, int rollCount, int archiveCount) {
		// 先查询当前状态
		String querySql = "SELECT COLLECT_STATUS FROM ARCHIVE_PUSH_LOG WHERE ID = '" + logId + "'";
		JSONArray result = Util.postQuerySql(querySql);

		// 如果当前状态已经是 SUCCESS，则不进行更新
		if (!result.isEmpty() && "SUCCESS".equals(result.getJSONObject(0).getStr("COLLECT_STATUS"))) {
			log.info("数据包[{}]已经是收集成功状态，跳过状态更新", logId);
			return;
		}

		String sql = "UPDATE ARCHIVE_PUSH_LOG SET COLLECT_STATUS = '" + status + "', COLLECT_MESSAGE = '" + message + "', " +
				"COLLECT_END_TIME = SYSDATE, PDF_COUNT = " + pdfCount + ", ROLL_COUNT = " + rollCount +
				", ARCHIVE_COUNT = " + archiveCount + " WHERE ID = '" + logId + "'";

		Util.postCommandSql(sql);
	}

	/**
	 * 更新推送状态
	 */
	protected void updatePushStatus(String logId, String status, String message, String zipPath) {
		String sql;
		if ("SUCCESS".equals(status)) {
			sql = "UPDATE ARCHIVE_PUSH_LOG SET PUSH_STATUS = '" + status + "', " +
					"PUSH_MESSAGE = '" + message + "', " +
					"PUSH_END_TIME = SYSDATE, " +
					"ZIP_PATH = '" + zipPath + "' " +
					"WHERE ID = '" + logId + "'";
		} else {
			sql = "UPDATE ARCHIVE_PUSH_LOG SET PUSH_STATUS = '" + status + "', " +
					"PUSH_MESSAGE = '" + message + "', " +
					"ZIP_PATH = '" + zipPath + "' " +
					"WHERE ID = '" + logId + "'";
		}

		Util.postCommandSql(sql);
	}

	/**
	 * 每天凌晨2点执行清理任务
	 */
	@Scheduled(cron = "0 0 2 * * ?")
	public void cleanOldPackages() {
		try {
			log.info("开始清理7天前的数据包文件...");

			// 获取7天前的时间戳
			long sevenDaysAgo = System.currentTimeMillis() - 7 * 24 * 60 * 60 * 1000;

			// 清理pushTempPath目录下的旧文件
			cleanOldFiles(pushTempPath, sevenDaysAgo);

			// 清理tempPath目录下的旧文件
			cleanOldFiles(tempPath, sevenDaysAgo);

			log.info("数据包文件清理完成");
		} catch (Exception e) {
			log.error("清理数据包文件时发生错误", e);
		}
	}

	/**
	 * 清理指定目录下的旧文件
	 */
	private void cleanOldFiles(String directoryPath, long beforeTime) {
		File directory = new File(directoryPath);
		if (!directory.exists() || !directory.isDirectory()) {
			log.warn("目录不存在或不是一个有效的目录: {}", directoryPath);
			return;
		}

		File[] files = directory.listFiles();
		if (files == null) {
			log.warn("无法列出目录中的文件: {}", directoryPath);
			return;
		}

		int deletedCount = 0;
		int failedCount = 0;

		for (File file : files) {
			try {
				// 使用文件的最后修改时间来判断
				long lastModified = file.lastModified();
				if (lastModified < beforeTime) {
					String fileName = file.getName();
					// 如果是目录，递归删除
					if (file.isDirectory()) {
						FileUtil.del(file);
						log.debug("已删除目录: {}", fileName);
					}
					// 如果是文件，直接删除
					else {
						FileUtil.del(file);
						log.debug("已删除文件: {}", fileName);
					}
					deletedCount++;
				}
			} catch (Exception e) {
				log.error("删除文件时发生错误: {}", file.getAbsolutePath(), e);
				failedCount++;
			}
		}

		log.info("目录 {} 清理完成: 成功删除 {} 个文件/目录, 失败 {} 个",
				directoryPath, deletedCount, failedCount);
	}

	/**
	 * 回调接口，用于处理files节点
	 */
	@FunctionalInterface
	protected interface CreateFilesCallback {
		void createFiles(Element roll, Element archive);
	}
}
