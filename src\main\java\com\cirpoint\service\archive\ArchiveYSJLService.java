package com.cirpoint.service.archive;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.cirpoint.util.ArchiveLogger;
import com.cirpoint.util.Util;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.cirpoint.util.CommonUtil.removeDecimalZero;

/**
 * 原始记录档案服务类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ArchiveYSJLService extends ArchiveBaseService {

	@Autowired
	private ArchivePackageService archivePackageService;

	// 原始记录案卷序号计数器
	private final AtomicInteger rollYSJLCounter = new AtomicInteger(1);


	/**
	 * 收集原始记录数据包
	 */
	public void collectYSJLPackage(String phaseTreeId, JSONObject phaseModelObj, ArchiveLogger logger) {
		try {
			logger.info("重置原始记录案卷序号计数器");
			rollYSJLCounter.set(1);

			logger.info("开始查询原始记录数据包信息");
			String sql = "select ID, REGEXP_REPLACE(NAME, '数据包$', '原始记录') as NAME, CREATETIME ,CODE " +
					"from DATAPKG_TREE_VIEW " +
					"where DIR_NAME not like '%外单位证明书、履历书%' " +
					"  and REFTREEID in " +
					"      (select TREEID from DATAPACKAGETREE start with TREEID =  " + phaseTreeId + " connect by prior TREEID = PARENTID)";

			logger.info("=== 执行SQL查询 [collectYSJLPackage] ===");
			logger.info("SQL语句: {}", sql);
			logger.info("参数: phaseTreeId = {}", phaseTreeId);

			List<JSONObject> successPackages = archivePackageService.processDataPackages(sql, phaseTreeId, phaseModelObj, "YSJL", (dpk, pkgPath) -> {
				try {
					logger.info(String.format("开始处理数据包: %s, 路径: %s", dpk.getStr("NAME"), pkgPath));
					createPackage(dpk, pkgPath, phaseModelObj, logger);
					logger.info(String.format("数据包 %s 处理完成", dpk.getStr("NAME")));
				} catch (Exception e) {
					logger.error(String.format("处理数据包 %s 失败", dpk.getStr("NAME")), e);
					throw new RuntimeException(e);
				}
			});

			if (successPackages.isEmpty()) {
				logger.info("没有找到需要处理的原始记录数据包");
				return;
			}

			logger.info(String.format("成功处理 %d 个原始记录数据包，开始异步推送", successPackages.size()));

			@SuppressWarnings("unchecked")
			CompletableFuture<Void>[] futures = new CompletableFuture[successPackages.size()];
			for (int i = 0; i < successPackages.size(); i++) {
				final JSONObject packageInfo = successPackages.get(i);
				futures[i] = CompletableFuture.runAsync(() -> {
					try {
						String pkgPath = packageInfo.getStr("pkgPath");
						String logId = packageInfo.getStr("logId");
						logger.info(String.format("开始推送数据包，路径: %s, 日志ID: %s", pkgPath, logId));
						archivePackageService.zipAndPushPackage(pkgPath, logId);
						logger.info(String.format("数据包推送完成，日志ID: %s", logId));
					} catch (Exception e) {
						logger.error("推送数据包失败", e);
						throw new RuntimeException(e);
					}
				});
			}
			CompletableFuture.allOf(futures)
					.exceptionally(ex -> {
						logger.error("部分数据包推送失败", ex);
						return null;
					});
			logger.info("原始记录数据包异步推送任务已创建");
		} catch (Exception e) {
			logger.error("收集原始记录数据包过程中发生错误", e);
			throw e;
		}
	}

	/**
	 * 创建数据包
	 */
	private void createPackage(JSONObject dpk, String pkgPath, JSONObject phaseModelObj,
							   ArchiveLogger logger) {
		createPackageXml(pkgPath, (roll, archive) ->
				createRoll(dpk, roll, archive, pkgPath, phaseModelObj, logger));
	}

	/**
	 * 创建案卷
	 */
	private void createRoll(JSONObject dpk, Element rollEl, Element archiveEl,
							String pkgPath, JSONObject phaseModelObj, ArchiveLogger logger) {
		String dpkId = removeDecimalZero(dpk.getStr("ID"));
		String dpkName = dpk.getStr("NAME", "");
		String dpkCode = dpk.getStr("CODE", "");
		String creator = phaseModelObj.getStr("pushUserFullname", "");
		String createTime = dpk.getStr("CREATETIME", "");
		String phaseNodeName = phaseModelObj.getStr("NODENAME");
		String archFilePath = pkgPath + dpkCode + "\\";
		FileUtil.mkdir(pkgPath);

		String innerId = dpkId + "_" + dpkCode;
		String rollId = String.format("0802(%s)YSJL-%03d", phaseNodeName, rollYSJLCounter.getAndIncrement());
		String rollDocName = "roll_" + dpkCode;
		String rollBuildTime = DateUtil.format(DateUtil.parse(createTime), "yyyy-MM-dd");

		createRollXml(innerId, dpkName, rollId, creator, rollBuildTime, dpkId,
				pkgPath, rollDocName, phaseNodeName, rollEl);

		//获取数据包下的所有pdf文件，按题名排序并生成档案序号
		String sql = "SELECT t.*, ROW_NUMBER() OVER (ORDER BY FILE_NAME) as ARCH_XH " +
				"FROM RESULTGATHER t " +
				"WHERE (FILE_FORMAT = 'pdf' OR FILE_FORMAT = 'PDF') AND FILEPATH IS NOT NULL " +
				"AND NODECODE = " + dpkId + " " +
				"ORDER BY FILE_NAME";

		logger.info("=== 执行SQL查询 [createRoll] ===");
		logger.info("SQL语句: {}", sql);
		logger.info("参数: dpkId = {}", dpkId);
		logger.info("参数: dpkName = {}", dpkName);
		logger.info("参数: dpkCode = {}", dpkCode);

		JSONArray files = Util.postQuerySql(sql);
		logger.info("查询到 {} 个文件，已按题名排序并分配档案序号", files.size());

		for (int i = 0; i < files.size(); i++) {
			JSONObject file = files.getJSONObject(i);
			createArch(file, archiveEl, archFilePath, phaseModelObj, rollId, logger);
		}
	}

	/**
	 * 创建档案XML和PDF文件
	 */
	private void createArch(JSONObject file, Element archiveEl, String archFilePath,
							JSONObject phaseModelObj, String rollNo, ArchiveLogger logger) {
		String id = removeDecimalZero(file.getStr("ID"));
		String tableName = file.getStr("TABLENAME", "");
		String innerId = tableName + "_" + id;
		String archTitle = file.getStr("FILE_NAME", ""); //题名
		String archXH = removeDecimalZero(file.getStr("ARCH_XH", "1")); //档案序号，从SQL查询结果获取并去除小数
		String secretId = getSecretId(file.getStr("SECURITY_LEVEL", "内部")); //密级
		String editDate = file.getStr("CREATE_TIMESTAMP", "").substring(0, 10); //编制日期 yyyy-MM-dd
		String srcFilePath = fileUploadPath + file.getStr("FILEPATH", ""); //文件路径

		logger.info("处理档案: 题名={}, 序号={}", archTitle, archXH);

		//判断文件是否存在
		if (FileUtil.exist(srcFilePath)) {
			String fileName = FileNameUtil.getName(srcFilePath) + ".pdf";
			createArchXml(srcFilePath, archFilePath, fileName, innerId, archXH, archTitle, secretId, editDate, rollNo,
					phaseModelObj, archiveEl, FileUtil.getParent(archFilePath, 1));
		}
	}
}
